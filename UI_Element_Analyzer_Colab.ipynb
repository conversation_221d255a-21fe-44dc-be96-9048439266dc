{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🔍 UI Element Analyzer with Enhanced Vector Database\n", "\n", "## Advanced UI Analysis using LangChain, LangGraph, Vector Databases, and Gemini AI\n", "\n", "This notebook implements a sophisticated workflow for analyzing UI elements:\n", "\n", "### 🔄 **Enhanced Workflow:**\n", "1. **Query Understanding**: Detect keywords in user queries\n", "2. **Vector Search in Coordinates DB**: Search for relevant UI element labels\n", "3. **Index Extraction**: Identify indices where labels are found\n", "4. **DOM Data Retrieval**: Use indices to retrieve complete DOM data from element_info.json\n", "5. **Context Injection**: Store DOM data in vector DB and pass as context\n", "6. **Coordinate Inclusion**: Include position information for spatial awareness\n", "7. **Model Response**: Generate position-aware responses using Gemini AI\n", "\n", "### 📊 **Key Features:**\n", "- ✅ Complete element_info.json storage in vector database indexed by position\n", "- ✅ Semantic search for UI element discovery\n", "- ✅ Position-aware AI responses with exact coordinates\n", "- ✅ Comprehensive DOM data integration\n", "- ✅ Interactive query interface\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 🚀 Setup and Installation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_packages"}, "outputs": [], "source": ["# Install required packages\n", "!pip install -q langchain>=0.1.0\n", "!pip install -q langgraph>=0.0.26\n", "!pip install -q langchain-google-genai>=0.0.6\n", "!pip install -q chromadb>=0.4.22\n", "!pip install -q pillow>=10.2.0\n", "!pip install -q numpy>=1.26.3\n", "!pip install -q sentence-transformers>=2.2.0\n", "!pip install -q google-generativeai\n", "\n", "print(\"✅ All packages installed successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "api_keys"}, "source": ["## 🔑 API Keys Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_keys"}, "outputs": [], "source": ["import os\n", "from google.colab import userdata\n", "import getpass\n", "\n", "# Try to get API keys from Colab secrets first\n", "try:\n", "    GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')\n", "    HUGGINGFACE_TOKEN = userdata.get('HUGGINGFACE_TOKEN')\n", "    print(\"✅ API keys loaded from Colab secrets\")\nexcept:\n", "    print(\"⚠️ API keys not found in secrets. Please enter them manually:\")\n", "    GOOGLE_API_KEY = getpass.getpass(\"Enter your Google API Key: \")\n", "    HUGGINGFACE_TOKEN = \"*************************************\"  # Default token\n", "\n", "# Set environment variables\n", "os.environ[\"GOOGLE_API_KEY\"] = GOOGLE_API_KEY\n", "os.environ[\"HUGGINGFACE_HUB_TOKEN\"] = HUGGINGFACE_TOKEN\n", "\n", "print(\"🔑 API keys configured successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "config"}, "source": ["## ⚙️ Configuration Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "config_class"}, "outputs": [], "source": ["# Configuration class\n", "class Config:\n", "    # API Keys\n", "    GOOGLE_API_KEY = os.getenv(\"GOOGLE_API_KEY\")\n", "    HUGGINGFACE_TOKEN = os.getenv(\"HUGGINGFACE_HUB_TOKEN\", \"*************************************\")\n", "    \n", "    # ChromaDB configuration\n", "    CHROMA_DB_PATH = \"./chroma_db\"\n", "    COLLECTION_NAME = \"ui_elements\"\n", "    \n", "    # Model configuration\n", "    MODEL_NAME = \"gemini-2.5-flash-preview-05-20\"\n", "    TEMPERATURE = 0.7\n", "    MAX_TOKENS = 10000\n", "    \n", "    # Vector database configuration\n", "    EMBEDDING_MODEL = \"all-MiniLM-L6-v2\"\n", "    TOP_K_RESULTS = 3\n", "    \n", "    # Default file paths\n", "    DEFAULT_SCREENSHOT_PATH = \"notioncom.png\"\n", "    DEFAULT_COORDINATES_PATH = \"coordinates.json\"\n", "    DEFAULT_ELEMENT_INFO_PATH = \"element_info.json\"\n", "\n", "config = Config()\n", "print(\"✅ Configuration loaded successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## 📦 Import Required Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "import_libraries"}, "outputs": [], "source": ["import json\n", "import re\n", "import chromadb\n", "import google.generativeai as genai\n", "from sentence_transformers import SentenceTransformer\n", "from typing import Dict, List, Any\n", "from PIL import Image\n", "import numpy as np\n", "import ipywidgets as widgets\n", "from IPython.display import display, HTML, clear_output\n", "import base64\n", "from io import BytesIO\n", "\n", "print(\"✅ All libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "core_classes"}, "source": ["## 🏗️ Core Classes Implementation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ui_element_processor"}, "outputs": [], "source": ["class UIElementProcessor:\n", "    \"\"\"Enhanced processor for UI elements with complete vector database workflow\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.config = config\n", "        \n", "        # Set Hugging Face token for model downloads\n", "        os.environ[\"HUGGINGFACE_HUB_TOKEN\"] = self.config.HUGGINGFACE_TOKEN\n", "        \n", "        try:\n", "            self.embeddings = SentenceTransformer(\n", "                self.config.EMBEDDING_MODEL,\n", "                token=self.config.HUGGINGFACE_TOKEN\n", "            )\n", "            print(\"✅ SentenceTransformer loaded successfully!\")\n", "        except Exception as e:\n", "            print(f\"❌ Could not load SentenceTransformer: {e}\")\n", "            self.embeddings = None\n", "            \n", "        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)\n", "        self.coordinates_collection = None\n", "        self.dom_collection = None\n", "        self._initialize_collections()\n", "\n", "        # Store raw data for index-based retrieval\n", "        self.coordinates_data = []\n", "        self.element_info_data = {}\n", "\n", "    def _initialize_collections(self):\n", "        \"\"\"Initialize or get existing ChromaDB collections for coordinates and DOM data\"\"\"\n", "        # Initialize coordinates collection\n", "        try:\n", "            self.coordinates_collection = self.chroma_client.get_collection(name=\"ui_coordinates\")\n", "            print(\"✅ Loaded existing coordinates collection\")\n", "        except:\n", "            self.coordinates_collection = self.chroma_client.create_collection(\n", "                name=\"ui_coordinates\",\n", "                metadata={\"description\": \"UI element coordinates and labels for semantic search\"}\n", "            )\n", "            print(\"✅ Created new coordinates collection\")\n", "\n", "        # Initialize DOM collection\n", "        try:\n", "            self.dom_collection = self.chroma_client.get_collection(name=\"ui_dom_data\")\n", "            print(\"✅ Loaded existing DOM collection\")\n", "        except:\n", "            self.dom_collection = self.chroma_client.create_collection(\n", "                name=\"ui_dom_data\",\n", "                metadata={\"description\": \"Complete DOM element information indexed by position\"}\n", "            )\n", "            print(\"✅ Created new DOM collection\")\n", "\n", "print(\"✅ UIElementProcessor class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "processor_methods"}, "outputs": [], "source": ["# Add methods to UIElementProcessor class\n", "def process_and_store_data(self, coordinates_data, element_info_data):\n", "    \"\"\"Enhanced processing and storage of UI element data in separate vector databases\"\"\"\n", "    self.coordinates_data = coordinates_data\n", "    self.element_info_data = element_info_data\n", "\n", "    # Clear existing data\n", "    try:\n", "        self.coordinates_collection.delete()\n", "        self.dom_collection.delete()\n", "    except:\n", "        pass\n", "\n", "    # Store coordinates data for semantic search\n", "    self._store_coordinates_data()\n", "\n", "    # Store complete DOM data indexed by position\n", "    self._store_dom_data()\n", "\n", "    print(f\"✅ Stored {len(self.coordinates_data)} coordinate elements and {len(self.element_info_data)} DOM elements\")\n", "\n", "def _store_coordinates_data(self):\n", "    \"\"\"Store coordinates data in vector database for semantic search\"\"\"\n", "    coord_documents = []\n", "    coord_metadatas = []\n", "    coord_ids = []\n", "\n", "    for i, coord_item in enumerate(self.coordinates_data):\n", "        # Create searchable text for coordinates\n", "        coord_text = self._create_coordinate_search_text(coord_item, i)\n", "\n", "        # Create metadata for coordinates\n", "        coord_metadata = {\n", "            \"index\": i,\n", "            \"element_id\": f\"element_{i+1}\",\n", "            \"label\": coord_item[\"label\"],\n", "            \"x\": coord_item[\"coordinates\"][\"x\"],\n", "            \"y\": coord_item[\"coordinates\"][\"y\"],\n", "            \"width\": coord_item[\"coordinates\"][\"width\"],\n", "            \"height\": coord_item[\"coordinates\"][\"height\"]\n", "        }\n", "\n", "        coord_documents.append(coord_text)\n", "        coord_metadatas.append(coord_metadata)\n", "        coord_ids.append(f\"coord_{i}\")\n", "\n", "    # Store coordinates in ChromaDB\n", "    if coord_documents:\n", "        self.coordinates_collection.add(\n", "            documents=coord_documents,\n", "            metadatas=coord_metadatas,\n", "            ids=coord_ids\n", "        )\n", "        print(f\"✅ Stored {len(coord_documents)} coordinate elements for semantic search\")\n", "\n", "# Bind methods to the class\n", "UIElementProcessor.process_and_store_data = process_and_store_data\n", "UIElementProcessor._store_coordinates_data = _store_coordinates_data\n", "\n", "print(\"✅ UIElementProcessor methods added!\")"]}, {"cell_type": "markdown", "metadata": {"id": "sample_data"}, "source": ["## 📊 Sample Data Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_sample_data"}, "outputs": [], "source": ["# Sample coordinates data\n", "sample_coordinates = [\n", "  {\n", "    \"coordinates\": { \"x\": 949, \"y\": 385, \"width\": 626, \"height\": 330 },\n", "    \"label\": \"Video\"\n", "  },\n", "  {\n", "    \"coordinates\": { \"x\": 323, \"y\": 451, \"width\": 602, \"height\": 128 },\n", "    \"label\": \"Main Heading\"\n", "  },\n", "  {\n", "    \"coordinates\": { \"x\": 725, \"y\": 2666, \"width\": 447, \"height\": 90 },\n", "    \"label\": \"Figure\"\n", "  }\n", "]\n", "\n", "# Sample element info data (simplified for demo)\n", "sample_element_info = {\n", "  \"element_1\": {\n", "    \"attributes\": {\n", "      \"autoplay\": \"\",\n", "      \"class\": \"Video_video__KYz0l Video_videoAspectRatio__qVTeE HomepageHero_image__5I40D\",\n", "      \"height\": \"540\",\n", "      \"muted\": \"\",\n", "      \"playsinline\": \"\",\n", "      \"poster\": \"/_next/image?url=%2Ffront-static%2Fnosey%2Fhero%2FnoseyHeroV2.png&w=2048&q=90\",\n", "      \"preload\": \"auto\",\n", "      \"src\": \"/front-static/nosey/hero/noseyHeroV2.mp4\",\n", "      \"style\": \"--video-aspect-ratio:1022 / 540\",\n", "      \"width\": \"1022\"\n", "    },\n", "    \"classes\": [\n", "      \"Video_video__KYz0l\",\n", "      \"Video_videoAspectRatio__qVTeE\",\n", "      \"HomepageHero_image__5I40D\"\n", "    ],\n", "    \"computedStyle\": {\n", "      \"display\": \"block\",\n", "      \"position\": \"static\",\n", "      \"width\": \"626px\",\n", "      \"height\": \"330.75px\",\n", "      \"object-fit\": \"contain\",\n", "      \"border-radius\": \"8px\"\n", "    },\n", "    \"cssSelector\": \"video.Video_video__KYz0l.Video_videoAspectRatio__qVTeE.HomepageHero_image__5I40D\",\n", "    \"href\": None,\n", "    \"id\": None,\n", "    \"inlineStyle\": \"--video-aspect-ratio: 1022 / 540;\",\n", "    \"src\": \"https://www.notion.com/front-static/nosey/hero/noseyHeroV2.mp4\",\n", "    \"tag\": \"video\",\n", "    \"text\": \"\",\n", "    \"xpath\": \"//*[@id=\\\"__next\\\"]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/div[2]/video[1]\"\n", "  },\n", "  \"element_2\": {\n", "    \"attributes\": {\n", "      \"class\": \"HomepageHero_heading__Nj93Y\"\n", "    },\n", "    \"classes\": [\n", "      \"HomepageHero_heading__Nj93Y\"\n", "    ],\n", "    \"computedStyle\": {\n", "      \"display\": \"block\",\n", "      \"font-size\": \"64px\",\n", "      \"font-weight\": \"600\",\n", "      \"line-height\": \"64px\",\n", "      \"color\": \"rgb(25, 25, 24)\"\n", "    },\n", "    \"cssSelector\": \"h1.HomepageHero_heading__Nj93Y\",\n", "    \"href\": None,\n", "    \"id\": None,\n", "    \"inlineStyle\": None,\n", "    \"src\": None,\n", "    \"tag\": \"h1\",\n", "    \"text\": \"The AI workspace that works for you.\",\n", "    \"xpath\": \"//*[@id=\\\"__next\\\"]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/header[1]/h1[1]\"\n", "  },\n", "  \"element_3\": {\n", "    \"attributes\": {\n", "      \"class\": \"spring_forbesQuote__qnv9M\"\n", "    },\n", "    \"classes\": [\n", "      \"spring_forbesQuote__qnv9M\"\n", "    ],\n", "    \"computedStyle\": {\n", "      \"display\": \"flex\",\n", "      \"align-items\": \"center\",\n", "      \"justify-content\": \"center\"\n", "    },\n", "    \"cssSelector\": \"figure.spring_forbesQuote__qnv9M\",\n", "    \"href\": None,\n", "    \"id\": None,\n", "    \"inlineStyle\": None,\n", "    \"src\": None,\n", "    \"tag\": \"figure\",\n", "    \"text\": \"Your AI everything app.\",\n", "    \"xpath\": \"//*[@id=\\\":RbmH4:\\\"]/div[2]/figure[1]\"\n", "  }\n", "}\n", "\n", "print(\"✅ Sample data loaded successfully!\")\n", "print(f\"📊 Coordinates: {len(sample_coordinates)} elements\")\n", "print(f\"🏗️ Element info: {len(sample_element_info)} elements\")"]}, {"cell_type": "markdown", "metadata": {"id": "additional_methods"}, "source": ["## 🔧 Additional Methods and UI Analyzer"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "helper_methods"}, "outputs": [], "source": ["# Add remaining methods to UIElementProcessor\n", "def _create_coordinate_search_text(self, coord_item, index):\n", "    \"\"\"Create searchable text for coordinate data\"\"\"\n", "    text_parts = [\n", "        f\"Label: {coord_item['label']}\",\n", "        f\"Element type: {coord_item['label'].lower()}\",\n", "        f\"UI element: {coord_item['label']}\",\n", "        f\"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}\",\n", "        f\"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}\",\n", "        f\"Index: {index}\",\n", "        f\"Element number: {index + 1}\"\n", "    ]\n", "    return \" | \".join(text_parts)\n", "\n", "def _store_dom_data(self):\n", "    \"\"\"Store complete DOM data indexed by position with enhanced metadata\"\"\"\n", "    dom_documents = []\n", "    dom_metadatas = []\n", "    dom_ids = []\n", "\n", "    for element_key, element_info in self.element_info_data.items():\n", "        # Extract index from element_key (e.g., \"element_1\" -> 0)\n", "        index = int(element_key.split('_')[1]) - 1\n", "\n", "        # Create comprehensive DOM text for embedding\n", "        dom_text = self._create_dom_search_text(element_info, index)\n", "\n", "        # Create comprehensive metadata including all DOM information\n", "        dom_metadata = {\n", "            \"index\": index,\n", "            \"element_id\": element_key,\n", "            \"tag\": element_info.get(\"tag\", \"\"),\n", "            \"text\": element_info.get(\"text\", \"\"),\n", "            \"css_selector\": element_info.get(\"cssSelector\", \"\"),\n", "            \"xpath\": element_info.get(\"xpath\", \"\"),\n", "            \"src\": element_info.get(\"src\", \"\"),\n", "            \"href\": element_info.get(\"href\", \"\"),\n", "            \"classes\": json.dumps(element_info.get(\"classes\", [])),\n", "            \"attributes\": json.dumps(element_info.get(\"attributes\", {})),\n", "            # Store the entire element_info as JSON for complete retrieval\n", "            \"full_element_data\": json.dumps(element_info),\n", "            # Add coordinate information if available\n", "            \"has_coordinates\": index < len(self.coordinates_data),\n", "            \"coordinate_label\": self.coordinates_data[index][\"label\"] if index < len(self.coordinates_data) else \"\",\n", "            \"coordinate_x\": self.coordinates_data[index][\"coordinates\"][\"x\"] if index < len(self.coordinates_data) else 0,\n", "            \"coordinate_y\": self.coordinates_data[index][\"coordinates\"][\"y\"] if index < len(self.coordinates_data) else 0,\n", "            \"coordinate_width\": self.coordinates_data[index][\"coordinates\"][\"width\"] if index < len(self.coordinates_data) else 0,\n", "            \"coordinate_height\": self.coordinates_data[index][\"coordinates\"][\"height\"] if index < len(self.coordinates_data) else 0\n", "        }\n", "\n", "        dom_documents.append(dom_text)\n", "        dom_metadatas.append(dom_metadata)\n", "        dom_ids.append(f\"dom_{index}\")\n", "\n", "    # Store DOM data in ChromaDB\n", "    if dom_documents:\n", "        self.dom_collection.add(\n", "            documents=dom_documents,\n", "            metadatas=dom_metadatas,\n", "            ids=dom_ids\n", "        )\n", "        print(f\"✅ Stored {len(dom_documents)} DOM elements with complete information\")\n", "\n", "def _create_dom_search_text(self, element_info, index):\n", "    \"\"\"Create comprehensive searchable text for DOM data\"\"\"\n", "    text_parts = [\n", "        f\"Tag: {element_info.get('tag', '')}\",\n", "        f\"Text content: {element_info.get('text', '')}\",\n", "        f\"CSS classes: {' '.join(element_info.get('classes', []))}\",\n", "        f\"CSS selector: {element_info.get('cssSelector', '')}\",\n", "        f\"XPath: {element_info.get('xpath', '')}\",\n", "        f\"Index: {index}\",\n", "        f\"Element number: {index + 1}\"\n", "    ]\n", "\n", "    # Add attributes if available\n", "    if 'attributes' in element_info:\n", "        attrs = element_info['attributes']\n", "        if attrs:\n", "            attr_text = \", \".join([f\"{k}={v}\" for k, v in attrs.items() if v])\n", "            text_parts.append(f\"Attributes: {attr_text}\")\n", "\n", "    return \" | \".join(text_parts)\n", "\n", "# Bind additional methods to the class\n", "UIElementProcessor._create_coordinate_search_text = _create_coordinate_search_text\n", "UIElementProcessor._store_dom_data = _store_dom_data\n", "UIElementProcessor._create_dom_search_text = _create_dom_search_text\n", "\n", "print(\"✅ Additional methods added to UIElementProcessor!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "ui_analyzer_class"}, "outputs": [], "source": ["class UIAnalyzer:\n", "    \"\"\"Enhanced UI analyzer using Gemini AI with comprehensive vector database workflow\"\"\"\n", "\n", "    def __init__(self):\n", "        self.config = config\n", "        self.processor = UIElementProcessor()\n", "\n", "        # Configure Gemini\n", "        genai.configure(api_key=self.config.GOOGLE_API_KEY)\n", "        self.model = genai.GenerativeModel(self.config.MODEL_NAME)\n", "        print(\"✅ UIAnalyzer initialized with enhanced vector database workflow!\")\n", "\n", "    def analyze_query(self, query: str) -> str:\n", "        \"\"\"\n", "        Enhanced query analysis following the complete workflow:\n", "        1. Query Understanding: Detect keywords\n", "        2. Vector Search in Coordinates DB: Search for labels\n", "        3. Index Extraction: Identify indices\n", "        4. DOM Data Retrieval: Get corresponding DOM data\n", "        5. Context Injection: Store DOM data and pass as context\n", "        6. Coordinate Inclusion: Include position information\n", "        7. Model Response: Generate position-aware response\n", "        \"\"\"\n", "        print(f\"\\n🔍 Analyzing query: '{query}'\")\n", "        print(\"=\" * 50)\n", "\n", "        # Use the enhanced workflow\n", "        analysis_result = self.processor.analyze_query_and_retrieve_context(query)\n", "\n", "        # Generate response using Gemini with enhanced context\n", "        response = self._generate_enhanced_response(query, analysis_result)\n", "\n", "        return response\n", "\n", "    def _generate_enhanced_response(self, query: str, analysis_result: Dict[str, Any]) -> str:\n", "        \"\"\"Generate response using Gemini with enhanced context from the workflow\"\"\"\n", "\n", "        # Extract information from analysis result\n", "        keywords = analysis_result[\"keywords\"]\n", "        combined_context = analysis_result[\"combined_context\"]\n", "        coordinate_matches = analysis_result[\"coordinate_matches\"]\n", "        dom_context = analysis_result[\"dom_context\"]\n", "\n", "        # Create enhanced prompt with workflow information\n", "        prompt = f\"\"\"\n", "You are an expert UI/UX analyst with access to comprehensive webpage analysis data. You have analyzed a user's question using an advanced workflow that includes:\n", "\n", "1. ✅ Query Understanding: Detected keywords: {', '.join(keywords)}\n", "2. ✅ Vector Search: Found {len(coordinate_matches)} relevant UI elements\n", "3. ✅ Index Extraction: Retrieved {len(dom_context)} complete DOM elements\n", "4. ✅ Context Integration: Combined coordinate and DOM information\n", "5. ✅ Position Awareness: Included exact pixel coordinates\n", "\n", "User Question: {query}\n", "\n", "COMPREHENSIVE UI ELEMENT ANALYSIS:\n", "{combined_context}\n", "\n", "ANALYSIS WORKFLOW SUMMARY:\n", "- Keywords detected: {', '.join(keywords)}\n", "- Elements found: {len(coordinate_matches)} coordinate matches\n", "- DOM data retrieved: {len(dom_context)} complete elements\n", "- Position data: Exact pixel coordinates included\n", "\n", "Please provide a comprehensive, position-aware response that:\n", "1. Directly addresses the user's question using the detected keywords\n", "2. References specific UI elements by their exact positions and properties\n", "3. Explains the functionality, purpose, and technical details of the elements\n", "4. Provides precise coordinate information (x, y, width, height)\n", "5. Includes relevant DOM information (HTML tags, CSS selectors, XPath)\n", "6. Uses clear, user-friendly language while being technically accurate\n", "7. Mentions how the element relates to the overall page structure\n", "\n", "Response:\n", "\"\"\"\n", "\n", "        try:\n", "            response = self.model.generate_content(prompt)\n", "            return response.text\n", "        except Exception as e:\n", "            return f\"❌ Error generating response: {str(e)}\"\n", "\n", "    def initialize_data(self, coordinates_data, element_info_data):\n", "        \"\"\"Initialize the enhanced system with UI data\"\"\"\n", "        print(f\"\\n📊 Initializing enhanced vector database system...\")\n", "        print(f\"📍 Coordinates: {len(coordinates_data)} elements\")\n", "        print(f\"🏗️ Element info: {len(element_info_data)} elements\")\n", "\n", "        self.processor.process_and_store_data(coordinates_data, element_info_data)\n", "        print(\"✅ Enhanced UI data initialized successfully!\")\n", "\n", "print(\"✅ UIAnalyzer class defined!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "workflow_methods"}, "outputs": [], "source": ["# Add workflow methods to UIElementProcessor\n", "def analyze_query_and_retrieve_context(self, query: str, top_k: int = None) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Enhanced query analysis following the specified workflow:\n", "    1. Query Understanding: Detect keywords\n", "    2. Vector Search in Coordinates DB: Search for labels\n", "    3. Index Extraction: Identify indices\n", "    4. DOM Data Retrieval: Get corresponding DOM data\n", "    5. Context Injection: Combine coordinate and DOM information\n", "    \"\"\"\n", "    if top_k is None:\n", "        top_k = self.config.TOP_K_RESULTS\n", "\n", "    # Step 1: Query Understanding - Extract keywords\n", "    keywords = self._extract_keywords(query)\n", "    print(f\"🔍 Detected keywords: {keywords}\")\n", "\n", "    # Step 2: Vector Search in Coordinates DB\n", "    coordinate_results = self._search_coordinates(query, top_k)\n", "    print(f\"📍 Found {len(coordinate_results)} relevant coordinate matches\")\n", "\n", "    # Step 3: Index Extraction\n", "    relevant_indices = self._extract_indices_from_coordinates(coordinate_results)\n", "    print(f\"📊 Extracted indices: {relevant_indices}\")\n", "\n", "    # Step 4: DOM Data Retrieval using indices\n", "    dom_context = self._retrieve_dom_data_by_indices(relevant_indices)\n", "    print(f\"🏗️ Retrieved DOM data for {len(dom_context)} elements\")\n", "\n", "    # Step 5: Context Injection - Combine coordinate and DOM information\n", "    combined_context = self._combine_coordinate_and_dom_context(coordinate_results, dom_context)\n", "\n", "    return {\n", "        \"keywords\": keywords,\n", "        \"coordinate_matches\": coordinate_results,\n", "        \"relevant_indices\": relevant_indices,\n", "        \"dom_context\": dom_context,\n", "        \"combined_context\": combined_context,\n", "        \"query\": query\n", "    }\n", "\n", "def _extract_keywords(self, query: str) -> List[str]:\n", "    \"\"\"Extract relevant keywords from the user query\"\"\"\n", "    # Convert to lowercase and split\n", "    words = re.findall(r'\\b\\w+\\b', query.lower())\n", "\n", "    # Filter out common stop words\n", "    stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}\n", "    keywords = [word for word in words if word not in stop_words and len(word) > 2]\n", "\n", "    return keywords\n", "\n", "def _search_coordinates(self, query: str, top_k: int) -> List[Dict[str, Any]]:\n", "    \"\"\"Search for relevant coordinates using vector search\"\"\"\n", "    if self.embeddings is not None:\n", "        # Use vector search if embeddings are available\n", "        results = self.coordinates_collection.query(\n", "            query_texts=[query],\n", "            n_results=top_k\n", "        )\n", "\n", "        coordinate_results = []\n", "        if results['documents'] and results['documents'][0]:\n", "            for i, doc in enumerate(results['documents'][0]):\n", "                metadata = results['metadatas'][0][i]\n", "                coordinate_results.append({\n", "                    \"document\": doc,\n", "                    \"metadata\": metadata,\n", "                    \"distance\": results['distances'][0][i] if 'distances' in results else None\n", "                })\n", "    else:\n", "        # Fallback to simple text matching\n", "        all_items = self.coordinates_collection.get()\n", "        coordinate_results = []\n", "\n", "        query_lower = query.lower()\n", "        for i, doc in enumerate(all_items['documents']):\n", "            metadata = all_items['metadatas'][i]\n", "            # Simple keyword matching\n", "            if any(keyword in doc.lower() for keyword in query_lower.split()):\n", "                coordinate_results.append({\n", "                    \"document\": doc,\n", "                    \"metadata\": metadata,\n", "                    \"distance\": 0.5  # Default similarity score\n", "                })\n", "\n", "        # Limit results\n", "        coordinate_results = coordinate_results[:top_k]\n", "\n", "    return coordinate_results\n", "\n", "# Bind workflow methods to the class\n", "UIElementProcessor.analyze_query_and_retrieve_context = analyze_query_and_retrieve_context\n", "UIElementProcessor._extract_keywords = _extract_keywords\n", "UIElementProcessor._search_coordinates = _search_coordinates\n", "\n", "print(\"✅ Workflow methods added to UIElementProcessor!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "remaining_methods"}, "outputs": [], "source": ["# Add remaining helper methods\n", "def _extract_indices_from_coordinates(self, coordinate_results: List[Dict[str, Any]]) -> List[int]:\n", "    \"\"\"Extract indices from coordinate search results\"\"\"\n", "    indices = []\n", "    for result in coordinate_results:\n", "        metadata = result[\"metadata\"]\n", "        if \"index\" in metadata:\n", "            indices.append(metadata[\"index\"])\n", "    return list(set(indices))  # Remove duplicates\n", "\n", "def _retrieve_dom_data_by_indices(self, indices: List[int]) -> List[Dict[str, Any]]:\n", "    \"\"\"Retrieve complete DOM data using the extracted indices\"\"\"\n", "    dom_context = []\n", "\n", "    for index in indices:\n", "        element_key = f\"element_{index + 1}\"\n", "        if element_key in self.element_info_data:\n", "            # Get the complete DOM information\n", "            element_info = self.element_info_data[element_key]\n", "\n", "            # Also get coordinate information for this index\n", "            coordinate_info = None\n", "            if index < len(self.coordinates_data):\n", "                coordinate_info = self.coordinates_data[index]\n", "\n", "            dom_context.append({\n", "                \"index\": index,\n", "                \"element_key\": element_key,\n", "                \"element_info\": element_info,\n", "                \"coordinate_info\": coordinate_info\n", "            })\n", "\n", "    return dom_context\n", "\n", "def _combine_coordinate_and_dom_context(self, coordinate_results: List[Dict[str, Any]], dom_context: List[Dict[str, Any]]) -> str:\n", "    \"\"\"Combine coordinate and DOM information into a comprehensive context\"\"\"\n", "    context_parts = []\n", "\n", "    # Create a mapping of indices to DOM data for easy lookup\n", "    dom_by_index = {item[\"index\"]: item for item in dom_context}\n", "\n", "    for coord_result in coordinate_results:\n", "        coord_metadata = coord_result[\"metadata\"]\n", "        index = coord_metadata.get(\"index\", -1)\n", "\n", "        if index in dom_by_index:\n", "            dom_item = dom_by_index[index]\n", "            element_info = dom_item[\"element_info\"]\n", "\n", "            # Extract key computed style properties for better context\n", "            computed_style = element_info.get('computedStyle', {})\n", "            key_styles = {}\n", "            important_style_props = ['display', 'position', 'width', 'height', 'color', 'background-color',\n", "                                   'font-size', 'font-weight', 'border-radius', 'opacity', 'z-index']\n", "            for prop in important_style_props:\n", "                if prop in computed_style:\n", "                    key_styles[prop] = computed_style[prop]\n", "\n", "            context_part = f\"\"\"\n", "Element {index + 1} ({coord_metadata['label']}):\n", "POSITION & SIZE:\n", "- Coordinates: ({coord_metadata['x']}, {coord_metadata['y']})\n", "- Dimensions: {coord_metadata['width']}px × {coord_metadata['height']}px\n", "- Area: {coord_metadata['width'] * coord_metadata['height']} square pixels\n", "\n", "DOM STRUCTURE:\n", "- HTML Tag: <{element_info.get('tag', 'unknown')}>\n", "- Text Content: \"{element_info.get('text', 'No text content')}\"\n", "- CSS Selector: {element_info.get('cssSelector', 'N/A')}\n", "- XPath: {element_info.get('xpath', 'N/A')}\n", "\n", "STYLING & ATTRIBUTES:\n", "- CSS Classes: {', '.join(element_info.get('classes', [])) or 'None'}\n", "- Key Attributes: {', '.join([f\"{k}='{v}'\" for k, v in element_info.get('attributes', {}).items() if v]) or 'None'}\n", "- Important Styles: {', '.join([f\"{k}: {v}\" for k, v in key_styles.items()]) or 'Default styles'}\n", "\n", "FUNCTIONALITY:\n", "- Source URL: {element_info.get('src', 'N/A')}\n", "- Link Target: {element_info.get('href', 'N/A')}\n", "- Inline Styles: {element_info.get('inlineStyle', 'None')}\n", "\"\"\"\n", "            context_parts.append(context_part.strip())\n", "\n", "    return \"\\n\\n\".join(context_parts)\n", "\n", "# Bind remaining methods to the class\n", "UIElementProcessor._extract_indices_from_coordinates = _extract_indices_from_coordinates\n", "UIElementProcessor._retrieve_dom_data_by_indices = _retrieve_dom_data_by_indices\n", "UIElementProcessor._combine_coordinate_and_dom_context = _combine_coordinate_and_dom_context\n", "\n", "print(\"✅ All helper methods added to UIElementProcessor!\")"]}, {"cell_type": "markdown", "metadata": {"id": "initialization"}, "source": ["## 🚀 System Initialization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "initialize_system"}, "outputs": [], "source": ["# Initialize the UI Analyzer with sample data\n", "print(\"🚀 Initializing Enhanced UI Element Analyzer\")\n", "print(\"=\" * 60)\n", "\n", "# Create analyzer instance\n", "analyzer = UIAnalyzer()\n", "\n", "# Initialize with sample data\n", "analyzer.initialize_data(sample_coordinates, sample_element_info)\n", "\n", "print(\"\\n🎉 System ready for queries!\")\n", "print(\"\\n📋 Available sample elements:\")\n", "for i, coord in enumerate(sample_coordinates):\n", "    print(f\"  {i+1}. {coord['label']} at ({coord['coordinates']['x']}, {coord['coordinates']['y']})\")"]}, {"cell_type": "markdown", "metadata": {"id": "interactive_interface"}, "source": ["## 💬 Interactive Query Interface"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "query_interface"}, "outputs": [], "source": ["# Interactive query interface\n", "def create_query_interface():\n", "    \"\"\"Create an interactive interface for querying UI elements\"\"\"\n", "    \n", "    # Example queries\n", "    example_queries = [\n", "        \"What is the video element on the page?\",\n", "        \"Where is the main heading shown on the page?\",\n", "        \"Tell me about the figure element and its location\",\n", "        \"What are the video attributes and properties?\",\n", "        \"Show me the CSS classes for the heading element\",\n", "        \"What is the XPath for the video element?\",\n", "        \"Where can I find elements with autoplay functionality?\"\n", "    ]\n", "    \n", "    # Create dropdown for example queries\n", "    query_dropdown = widgets.Dropdown(\n", "        options=[\"Select an example...\"] + example_queries,\n", "        value=\"Select an example...\",\n", "        description='Examples:',\n", "        style={'description_width': 'initial'},\n", "        layout=widgets.Layout(width='100%')\n", "    )\n", "    \n", "    # Create text area for custom queries\n", "    query_text = widgets.Textarea(\n", "        value='',\n", "        placeholder='Enter your question about UI elements...',\n", "        description='Your Query:',\n", "        style={'description_width': 'initial'},\n", "        layout=widgets.Layout(width='100%', height='100px')\n", "    )\n", "    \n", "    # Create analyze button\n", "    analyze_button = widgets.Button(\n", "        description='🔍 Analyze Query',\n", "        button_style='primary',\n", "        layout=widgets.Layout(width='200px')\n", "    )\n", "    \n", "    # Create output area\n", "    output_area = widgets.Output()\n", "    \n", "    def on_dropdown_change(change):\n", "        if change['new'] != \"Select an example...\":\n", "            query_text.value = change['new']\n", "    \n", "    def on_analyze_click(button):\n", "        with output_area:\n", "            clear_output(wait=True)\n", "            \n", "            query = query_text.value.strip()\n", "            if not query:\n", "                print(\"⚠️ Please enter a query first!\")\n", "                return\n", "            \n", "            try:\n", "                print(f\"🔍 Processing query: '{query}'\")\n", "                print(\"=\" * 60)\n", "                \n", "                # Analyze the query\n", "                response = analyzer.analyze_query(query)\n", "                \n", "                print(\"\\n🤖 AI Response:\")\n", "                print(\"=\" * 60)\n", "                print(response)\n", "                \n", "            except Exception as e:\n", "                print(f\"❌ Error processing query: {str(e)}\")\n", "    \n", "    # Bind events\n", "    query_dropdown.observe(on_dropdown_change, names='value')\n", "    analyze_button.on_click(on_analyze_click)\n", "    \n", "    # Display interface\n", "    display(widgets.VBox([\n", "        widgets.HTML(\"<h3>🔍 Enhanced UI Element Query Interface</h3>\"),\n", "        widgets.HTML(\"<p>Ask questions about UI elements and get detailed, position-aware responses!</p>\"),\n", "        query_dropdown,\n", "        query_text,\n", "        analyze_button,\n", "        output_area\n", "    ]))\n", "\n", "# Create and display the interface\n", "create_query_interface()"]}, {"cell_type": "markdown", "metadata": {"id": "testing_examples"}, "source": ["## 🧪 Testing Examples\n", "\n", "### Try these example queries to see the enhanced workflow in action:\n", "\n", "1. **\"What is the video element on the page?\"**\n", "   - Tests video element detection and attribute analysis\n", "\n", "2. **\"Where is the main heading shown on the page?\"**\n", "   - Tests position-aware responses for text elements\n", "\n", "3. **\"Tell me about the figure element and its location\"**\n", "   - Tests comprehensive element analysis\n", "\n", "4. **\"What are the video attributes and properties?\"**\n", "   - Tests detailed DOM attribute extraction\n", "\n", "5. **\"Show me the CSS classes for the heading element\"**\n", "   - Tests CSS class and styling information\n", "\n", "### 🔄 Enhanced Workflow Demonstration:\n", "\n", "Each query follows this workflow:\n", "1. **Query Understanding**: Keywords are extracted\n", "2. **Vector Search**: Relevant elements found in coordinates DB\n", "3. **Index Extraction**: Element indices identified\n", "4. **DOM Retrieval**: Complete element data retrieved\n", "5. **Context Injection**: Information combined for AI\n", "6. **Position-Aware Response**: Detailed answer with coordinates\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "file_upload"}, "source": ["## 📁 Upload Your Own Data\n", "\n", "You can upload your own UI data files to analyze custom web pages!"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_interface"}, "outputs": [], "source": ["from google.colab import files\n", "import json\n", "\n", "def upload_custom_data():\n", "    \"\"\"Upload and process custom UI data files\"\"\"\n", "    \n", "    print(\"📁 Upload Your Custom UI Data Files\")\n", "    print(\"=\" * 50)\n", "    print(\"Please upload the following files:\")\n", "    print(\"1. coordinates.json - UI element positions and labels\")\n", "    print(\"2. element_info.json - Complete DOM data for elements\")\n", "    print(\"\\nNote: Files should follow the same format as the sample data.\")\n", "    \n", "    # Upload coordinates file\n", "    print(\"\\n📍 Upload coordinates.json:\")\n", "    coord_files = files.upload()\n", "    \n", "    # Upload element info file\n", "    print(\"\\n🏗️ Upload element_info.json:\")\n", "    element_files = files.upload()\n", "    \n", "    try:\n", "        # Process uploaded files\n", "        coord_filename = list(coord_files.keys())[0]\n", "        element_filename = list(element_files.keys())[0]\n", "        \n", "        # Load the data\n", "        with open(coord_filename, 'r') as f:\n", "            custom_coordinates = json.load(f)\n", "        \n", "        with open(element_filename, 'r') as f:\n", "            custom_element_info = json.load(f)\n", "        \n", "        print(f\"\\n✅ Files loaded successfully!\")\n", "        print(f\"📊 Coordinates: {len(custom_coordinates)} elements\")\n", "        print(f\"🏗️ Element info: {len(custom_element_info)} elements\")\n", "        \n", "        # Initialize analyzer with custom data\n", "        print(\"\\n🔄 Initializing analyzer with your data...\")\n", "        analyzer.initialize_data(custom_coordinates, custom_element_info)\n", "        \n", "        print(\"\\n🎉 Your custom data is now ready for analysis!\")\n", "        print(\"Use the query interface above to ask questions about your UI elements.\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error processing uploaded files: {str(e)}\")\n", "        print(\"Please check that your files are in the correct JSON format.\")\n", "        return False\n", "\n", "# Create upload button\n", "upload_button = widgets.Button(\n", "    description='📁 Upload Custom Data',\n", "    button_style='info',\n", "    layout=widgets.Layout(width='200px')\n", ")\n", "\n", "upload_output = widgets.Output()\n", "\n", "def on_upload_click(button):\n", "    with upload_output:\n", "        clear_output(wait=True)\n", "        upload_custom_data()\n", "\n", "upload_button.on_click(on_upload_click)\n", "\n", "display(widgets.VBox([\n", "    widgets.HTML(\"<h3>📁 Custom Data Upload</h3>\"),\n", "    widgets.HTML(\"<p>Upload your own coordinates.json and element_info.json files to analyze custom web pages.</p>\"),\n", "    upload_button,\n", "    upload_output\n", "]))"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}