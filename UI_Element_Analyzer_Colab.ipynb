# Install required packages
!pip install -q langchain>=0.1.0
!pip install -q langgraph>=0.0.26
!pip install -q langchain-google-genai>=0.0.6
!pip install -q chromadb>=0.4.22
!pip install -q pillow>=10.2.0
!pip install -q numpy>=1.26.3
!pip install -q sentence-transformers>=2.2.0
!pip install -q google-generativeai

print("✅ All packages installed successfully!")

import os
from google.colab import userdata
import getpass

# Try to get API keys from Colab secrets first
try:
    GOOGLE_API_KEY = userdata.get('GOOGLE_API_KEY')
    HUGGINGFACE_TOKEN = userdata.get('HUGGINGFACE_TOKEN')
    print("✅ API keys loaded from Colab secrets")
except:
    print("⚠️ API keys not found in secrets. Please enter them manually:")
    GOOGLE_API_KEY = getpass.getpass("Enter your Google API Key: ")
    HUGGINGFACE_TOKEN = "*************************************"  # Default token

# Set environment variables
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY
os.environ["HUGGINGFACE_HUB_TOKEN"] = HUGGINGFACE_TOKEN

print("🔑 API keys configured successfully!")

# Configuration class
class Config:
    # API Keys
    GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
    HUGGINGFACE_TOKEN = os.getenv("HUGGINGFACE_HUB_TOKEN", "*************************************")
    
    # ChromaDB configuration
    CHROMA_DB_PATH = "./chroma_db"
    COLLECTION_NAME = "ui_elements"
    
    # Model configuration
    MODEL_NAME = "gemini-2.5-flash-preview-05-20"
    TEMPERATURE = 0.7
    MAX_TOKENS = 10000
    
    # Vector database configuration
    EMBEDDING_MODEL = "all-MiniLM-L6-v2"
    TOP_K_RESULTS = 3
    
    # Default file paths
    DEFAULT_SCREENSHOT_PATH = "notioncom.png"
    DEFAULT_COORDINATES_PATH = "coordinates.json"
    DEFAULT_ELEMENT_INFO_PATH = "element_info.json"
    DEFAULT_LABELS_PATH = "label.txt"

config = Config()
print("✅ Configuration loaded successfully!")

import json
import re
import chromadb
import google.generativeai as genai
from sentence_transformers import SentenceTransformer
from typing import Dict, List, Any, Set
from PIL import Image
import numpy as np
from difflib import SequenceMatcher
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output
import base64
from io import BytesIO

print("✅ All libraries imported successfully!")

class UIElementProcessor:
    """Enhanced processor for UI elements with complete vector database workflow"""
    
    def __init__(self):
        self.config = config
        
        # Set Hugging Face token for model downloads
        os.environ["HUGGINGFACE_HUB_TOKEN"] = self.config.HUGGINGFACE_TOKEN
        
        try:
            self.embeddings = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                token=self.config.HUGGINGFACE_TOKEN
            )
            print("✅ SentenceTransformer loaded successfully!")
        except Exception as e:
            print(f"❌ Could not load SentenceTransformer: {e}")
            self.embeddings = None
            
        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)
        self.coordinates_collection = None
        self.dom_collection = None
        self._initialize_collections()

        # Store raw data for index-based retrieval
        self.coordinates_data = []
        self.element_info_data = {}
        
        # Load controlled vocabulary from label.txt
        self.controlled_vocabulary = self._load_controlled_vocabulary()

    def _initialize_collections(self):
        """Initialize or get existing ChromaDB collections for coordinates and DOM data"""
        # Initialize coordinates collection
        try:
            self.coordinates_collection = self.chroma_client.get_collection(name="ui_coordinates")
            print("✅ Loaded existing coordinates collection")
        except:
            self.coordinates_collection = self.chroma_client.create_collection(
                name="ui_coordinates",
                metadata={"description": "UI element coordinates and labels for semantic search"}
            )
            print("✅ Created new coordinates collection")

        # Initialize DOM collection
        try:
            self.dom_collection = self.chroma_client.get_collection(name="ui_dom_data")
            print("✅ Loaded existing DOM collection")
        except:
            self.dom_collection = self.chroma_client.create_collection(
                name="ui_dom_data",
                metadata={"description": "Complete DOM element information indexed by position"}
            )
            print("✅ Created new DOM collection")

print("✅ UIElementProcessor class defined!")

# Add methods to UIElementProcessor class
def process_and_store_data(self, coordinates_data, element_info_data):
    """Enhanced processing and storage of UI element data in separate vector databases"""
    self.coordinates_data = coordinates_data
    self.element_info_data = element_info_data

    # Clear existing data
    try:
        self.coordinates_collection.delete()
        self.dom_collection.delete()
    except:
        pass

    # Store coordinates data for semantic search
    self._store_coordinates_data()

    # Store complete DOM data indexed by position
    self._store_dom_data()

    print(f"✅ Stored {len(self.coordinates_data)} coordinate elements and {len(self.element_info_data)} DOM elements")

def _store_coordinates_data(self):
    """Store coordinates data in vector database for semantic search"""
    coord_documents = []
    coord_metadatas = []
    coord_ids = []

    for i, coord_item in enumerate(self.coordinates_data):
        # Create searchable text for coordinates
        coord_text = self._create_coordinate_search_text(coord_item, i)

        # Create metadata for coordinates
        coord_metadata = {
            "index": i,
            "element_id": f"element_{i+1}",
            "label": coord_item["label"],
            "x": coord_item["coordinates"]["x"],
            "y": coord_item["coordinates"]["y"],
            "width": coord_item["coordinates"]["width"],
            "height": coord_item["coordinates"]["height"]
        }

        coord_documents.append(coord_text)
        coord_metadatas.append(coord_metadata)
        coord_ids.append(f"coord_{i}")

    # Store coordinates in ChromaDB
    if coord_documents:
        self.coordinates_collection.add(
            documents=coord_documents,
            metadatas=coord_metadatas,
            ids=coord_ids
        )
        print(f"✅ Stored {len(coord_documents)} coordinate elements for semantic search")

# Bind methods to the class
UIElementProcessor.process_and_store_data = process_and_store_data
UIElementProcessor._store_coordinates_data = _store_coordinates_data

print("✅ UIElementProcessor methods added!")

# Sample coordinates data
sample_coordinates = [
  {
    "coordinates": { "x": 949, "y": 385, "width": 626, "height": 330 },
    "label": "Video"
  },
  {
    "coordinates": { "x": 323, "y": 451, "width": 602, "height": 128 },
    "label": "Main Heading"
  },
  {
    "coordinates": { "x": 725, "y": 2666, "width": 447, "height": 90 },
    "label": "Figure"
  }
]

# Sample element info data (simplified for demo)
sample_element_info = {
  "element_1": {
    "attributes": {
      "autoplay": "",
      "class": "Video_video__KYz0l Video_videoAspectRatio__qVTeE HomepageHero_image__5I40D",
      "height": "540",
      "muted": "",
      "playsinline": "",
      "poster": "/_next/image?url=%2Ffront-static%2Fnosey%2Fhero%2FnoseyHeroV2.png&w=2048&q=90",
      "preload": "auto",
      "src": "/front-static/nosey/hero/noseyHeroV2.mp4",
      "style": "--video-aspect-ratio:1022 / 540",
      "width": "1022"
    },
    "classes": [
      "Video_video__KYz0l",
      "Video_videoAspectRatio__qVTeE",
      "HomepageHero_image__5I40D"
    ],
    "computedStyle": {
      "display": "block",
      "position": "static",
      "width": "626px",
      "height": "330.75px",
      "object-fit": "contain",
      "border-radius": "8px"
    },
    "cssSelector": "video.Video_video__KYz0l.Video_videoAspectRatio__qVTeE.HomepageHero_image__5I40D",
    "href": None,
    "id": None,
    "inlineStyle": "--video-aspect-ratio: 1022 / 540;",
    "src": "https://www.notion.com/front-static/nosey/hero/noseyHeroV2.mp4",
    "tag": "video",
    "text": "",
    "xpath": "//*[@id=\"__next\"]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/div[2]/video[1]"
  },
  "element_2": {
    "attributes": {
      "class": "HomepageHero_heading__Nj93Y"
    },
    "classes": [
      "HomepageHero_heading__Nj93Y"
    ],
    "computedStyle": {
      "display": "block",
      "font-size": "64px",
      "font-weight": "600",
      "line-height": "64px",
      "color": "rgb(25, 25, 24)"
    },
    "cssSelector": "h1.HomepageHero_heading__Nj93Y",
    "href": None,
    "id": None,
    "inlineStyle": None,
    "src": None,
    "tag": "h1",
    "text": "The AI workspace that works for you.",
    "xpath": "//*[@id=\"__next\"]/div[1]/div[1]/main[1]/div[1]/div[1]/section[1]/header[1]/h1[1]"
  },
  "element_3": {
    "attributes": {
      "class": "spring_forbesQuote__qnv9M"
    },
    "classes": [
      "spring_forbesQuote__qnv9M"
    ],
    "computedStyle": {
      "display": "flex",
      "align-items": "center",
      "justify-content": "center"
    },
    "cssSelector": "figure.spring_forbesQuote__qnv9M",
    "href": None,
    "id": None,
    "inlineStyle": None,
    "src": None,
    "tag": "figure",
    "text": "Your AI everything app.",
    "xpath": "//*[@id=\":RbmH4:\"]/div[2]/figure[1]"
  }
}

print("✅ Sample data loaded successfully!")
print(f"📊 Coordinates: {len(sample_coordinates)} elements")
print(f"🏗️ Element info: {len(sample_element_info)} elements")

# Add remaining methods to UIElementProcessor
def _create_coordinate_search_text(self, coord_item, index):
    """Create searchable text for coordinate data"""
    text_parts = [
        f"Label: {coord_item['label']}",
        f"Element type: {coord_item['label'].lower()}",
        f"UI element: {coord_item['label']}",
        f"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}",
        f"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}",
        f"Index: {index}",
        f"Element number: {index + 1}"
    ]
    return " | ".join(text_parts)

def _store_dom_data(self):
    """Store complete DOM data indexed by position with enhanced metadata"""
    dom_documents = []
    dom_metadatas = []
    dom_ids = []

    for element_key, element_info in self.element_info_data.items():
        # Extract index from element_key (e.g., "element_1" -> 0)
        index = int(element_key.split('_')[1]) - 1

        # Create comprehensive DOM text for embedding
        dom_text = self._create_dom_search_text(element_info, index)

        # Create comprehensive metadata including all DOM information
        dom_metadata = {
            "index": index,
            "element_id": element_key,
            "tag": element_info.get("tag", ""),
            "text": element_info.get("text", ""),
            "css_selector": element_info.get("cssSelector", ""),
            "xpath": element_info.get("xpath", ""),
            "src": element_info.get("src", ""),
            "href": element_info.get("href", ""),
            "classes": json.dumps(element_info.get("classes", [])),
            "attributes": json.dumps(element_info.get("attributes", {})),
            # Store the entire element_info as JSON for complete retrieval
            "full_element_data": json.dumps(element_info),
            # Add coordinate information if available
            "has_coordinates": index < len(self.coordinates_data),
            "coordinate_label": self.coordinates_data[index]["label"] if index < len(self.coordinates_data) else "",
            "coordinate_x": self.coordinates_data[index]["coordinates"]["x"] if index < len(self.coordinates_data) else 0,
            "coordinate_y": self.coordinates_data[index]["coordinates"]["y"] if index < len(self.coordinates_data) else 0,
            "coordinate_width": self.coordinates_data[index]["coordinates"]["width"] if index < len(self.coordinates_data) else 0,
            "coordinate_height": self.coordinates_data[index]["coordinates"]["height"] if index < len(self.coordinates_data) else 0
        }

        dom_documents.append(dom_text)
        dom_metadatas.append(dom_metadata)
        dom_ids.append(f"dom_{index}")

    # Store DOM data in ChromaDB
    if dom_documents:
        self.dom_collection.add(
            documents=dom_documents,
            metadatas=dom_metadatas,
            ids=dom_ids
        )
        print(f"✅ Stored {len(dom_documents)} DOM elements with complete information")

def _create_dom_search_text(self, element_info, index):
    """Create comprehensive searchable text for DOM data"""
    text_parts = [
        f"Tag: {element_info.get('tag', '')}",
        f"Text content: {element_info.get('text', '')}",
        f"CSS classes: {' '.join(element_info.get('classes', []))}",
        f"CSS selector: {element_info.get('cssSelector', '')}",
        f"XPath: {element_info.get('xpath', '')}",
        f"Index: {index}",
        f"Element number: {index + 1}"
    ]

    # Add attributes if available
    if 'attributes' in element_info:
        attrs = element_info['attributes']
        if attrs:
            attr_text = ", ".join([f"{k}={v}" for k, v in attrs.items() if v])
            text_parts.append(f"Attributes: {attr_text}")

    return " | ".join(text_parts)

# Bind additional methods to the class
UIElementProcessor._create_coordinate_search_text = _create_coordinate_search_text
UIElementProcessor._store_dom_data = _store_dom_data
UIElementProcessor._create_dom_search_text = _create_dom_search_text

print("✅ Additional methods added to UIElementProcessor!")

# Add intelligent label detection methods
def _identify_target_label(self, query_lower):
    """Intelligently identify the specific UI element label the user is asking about"""
    # Define query patterns that indicate the user is asking about a specific element
    element_patterns = [
        r'what is the (\w+)',
        r'where is the (\w+)',
        r'show me the (\w+)',
        r'find the (\w+)',
        r'tell me about the (\w+)',
        r'(\w+) element',
        r'the (\w+) on the page',
        r'(\w+) attributes',
        r'(\w+) properties',
        r'css classes for the (\w+)',
        r'xpath for the (\w+)',
    ]
    
    # Try to extract the target element from query patterns
    for pattern in element_patterns:
        matches = re.findall(pattern, query_lower)
        for match in matches:
            # Check if this match exists in our controlled vocabulary
            if match in self.controlled_vocabulary:
                return match
            
            # Check for fuzzy matches
            fuzzy_match = self._find_best_fuzzy_match(match)
            if fuzzy_match:
                print(f"🔍 Fuzzy match: '{match}' → '{fuzzy_match}'")
                return fuzzy_match
    
    # Try to find multi-word labels that appear in the query
    for label in self.controlled_vocabulary:
        if len(label.split()) > 1:  # Multi-word labels
            if label in query_lower:
                return label
    
    return None

def _find_best_fuzzy_match(self, word):
    """Find the best fuzzy match for a word in the controlled vocabulary"""
    if len(word) < 3:  # Skip very short words
        return None
        
    best_match = None
    best_ratio = 0.0
    
    for vocab_term in self.controlled_vocabulary:
        # Only consider single words for fuzzy matching
        if len(vocab_term.split()) == 1:
            ratio = SequenceMatcher(None, word, vocab_term).ratio()
            if ratio > best_ratio and ratio > 0.8:  # 80% similarity threshold
                best_ratio = ratio
                best_match = vocab_term
    
    return best_match

# Enhanced keyword extraction with intelligent target detection
def _extract_keywords_enhanced(self, query):
    """Enhanced keyword extraction using intelligent target label detection"""
    if not self.controlled_vocabulary:
        # Fallback to original method if no controlled vocabulary
        words = re.findall(r'\b\w+\b', query.lower())
        stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}
        return [word for word in words if word not in stop_words and len(word) > 2]
    
    query_lower = query.lower()
    
    # Step 1: Identify the target UI element label
    target_label = self._identify_target_label(query_lower)
    
    if target_label:
        print(f"🎯 Target UI element identified: '{target_label}'")
        return [target_label]
    
    # Step 2: Fallback to general keyword extraction if no specific target found
    matched_keywords = []
    words = re.findall(r'\b\w+\b', query_lower)
    for word in words:
        if word in self.controlled_vocabulary and word not in matched_keywords:
            matched_keywords.append(word)
            print(f"🎯 Vocabulary match found: '{word}'")
    
    print(f"🔍 Final matched keywords from controlled vocabulary: {matched_keywords}")
    return matched_keywords

# Bind intelligent detection methods to the class
UIElementProcessor._identify_target_label = _identify_target_label
UIElementProcessor._find_best_fuzzy_match = _find_best_fuzzy_match
UIElementProcessor._extract_keywords = _extract_keywords_enhanced

print("✅ Intelligent label detection methods added!")

# Add enhanced coordinate search with target label detection
def _search_coordinates_enhanced(self, query, top_k=3):
    """Enhanced coordinate search using intelligent label detection"""
    
    # First, try to identify the target label from the query
    target_label = self._identify_target_label(query.lower())
    
    if target_label:
        # Search specifically for the identified target label
        coordinate_results = self._search_by_target_label(target_label)
        if coordinate_results:
            print(f"🎯 Found exact match for target label: '{target_label}'")
            return coordinate_results
    
    # Fallback to general search if no specific target found
    return self._search_coordinates_general(query, top_k)

def _search_by_target_label(self, target_label):
    """Search for coordinates that match the specific target label"""
    coordinate_results = []
    
    # Get all items from the collection
    all_items = self.coordinates_collection.get()
    
    for i, doc in enumerate(all_items['documents']):
        metadata = all_items['metadatas'][i]
        label = metadata.get('label', '').lower()
        
        # Check for exact label match or partial match
        if (target_label == label or 
            target_label in label or 
            label in target_label or
            self._labels_are_similar(target_label, label)):
            
            coordinate_results.append({
                "document": doc,
                "metadata": metadata,
                "distance": 0.1,  # High confidence for exact matches
                "match_type": "target_label"
            })
            print(f"📍 Target label match: '{target_label}' → '{label}'")
    
    return coordinate_results

def _labels_are_similar(self, label1, label2):
    """Check if two labels are similar enough to be considered a match"""
    # Handle common variations
    variations = {
        'video': ['video embeds', 'embedded player', 'video with controls'],
        'heading': ['main heading', 'page heading', 'section heading', 'sub heading', 'card heading'],
        'button': ['primary button', 'secondary button', 'icon button', 'ghost button'],
        'image': ['standard image', 'interactive image', 'thumbnail image', 'image with caption'],
        'navigation': ['navigation bars', 'top navigation bar', 'sidebar navigation', 'bottom navigation'],
        'menu': ['dropdown menu', 'context menu', 'hamburger menu'],
        'icon': ['standard icon', 'interactive icon', 'status icon', 'social icon'],
        'form': ['basic form', 'login form', 'signup form', 'multi-step form'],
        'card': ['standard card', 'image card', 'testimonial card'],
        'list': ['ordered list', 'unordered list', 'bulleted list'],
        'tab': ['standard tab', 'icon tab', 'scrollable tab bar'],
        'slider': ['continuous slider', 'discrete slider', 'range slider'],
        'checkbox': ['basic checkbox', 'tri-state checkbox', 'disabled checkbox'],
        'toggle': ['basic toggle', 'labeled toggle', 'on/off toggle'],
        'dropdown': ['multi-select dropdown', 'searchable dropdown', 'disabled dropdown']
    }
    
    # Check if label1 is a base form of label2 or vice versa
    for base, variants in variations.items():
        if label1 == base and any(variant in label2 for variant in variants):
            return True
        if label2 == base and any(variant in label1 for variant in variants):
            return True
    
    return False

def _search_coordinates_general(self, query, top_k):
    """General coordinate search using vector search or text matching"""
    # Fallback to simple text matching for Colab
    all_items = self.coordinates_collection.get()
    coordinate_results = []

    query_lower = query.lower()
    for i, doc in enumerate(all_items['documents']):
        metadata = all_items['metadatas'][i]
        # Simple keyword matching
        if any(keyword in doc.lower() for keyword in query_lower.split()):
            coordinate_results.append({
                "document": doc,
                "metadata": metadata,
                "distance": 0.5,  # Default similarity score
                "match_type": "text_matching"
            })

    # Limit results
    coordinate_results = coordinate_results[:top_k]
    return coordinate_results

# Bind enhanced search methods to the class
UIElementProcessor._search_coordinates = _search_coordinates_enhanced
UIElementProcessor._search_by_target_label = _search_by_target_label
UIElementProcessor._labels_are_similar = _labels_are_similar
UIElementProcessor._search_coordinates_general = _search_coordinates_general

print("✅ Enhanced coordinate search with intelligent target detection added!")

class UIAnalyzer:
    """Enhanced UI analyzer using Gemini AI with comprehensive vector database workflow"""

    def __init__(self):
        self.config = config
        self.processor = UIElementProcessor()

        # Configure Gemini
        genai.configure(api_key=self.config.GOOGLE_API_KEY)
        self.model = genai.GenerativeModel(self.config.MODEL_NAME)
        print("✅ UIAnalyzer initialized with enhanced vector database workflow!")

    def analyze_query(self, query: str) -> str:
        """
        Enhanced query analysis following the complete workflow:
        1. Query Understanding: Detect keywords
        2. Vector Search in Coordinates DB: Search for labels
        3. Index Extraction: Identify indices
        4. DOM Data Retrieval: Get corresponding DOM data
        5. Context Injection: Store DOM data and pass as context
        6. Coordinate Inclusion: Include position information
        7. Model Response: Generate position-aware response
        """
        print(f"\n🔍 Analyzing query: '{query}'")
        print("=" * 50)

        # Use the enhanced workflow
        analysis_result = self.processor.analyze_query_and_retrieve_context(query)

        # Generate response using Gemini with enhanced context
        response = self._generate_enhanced_response(query, analysis_result)

        return response

    def _generate_enhanced_response(self, query: str, analysis_result: Dict[str, Any]) -> str:
        """Generate response using Gemini with enhanced context from the workflow"""

        # Extract information from analysis result
        keywords = analysis_result["keywords"]
        combined_context = analysis_result["combined_context"]
        coordinate_matches = analysis_result["coordinate_matches"]
        dom_context = analysis_result["dom_context"]

        # Create enhanced prompt with workflow information
        prompt = f"""
You are an expert UI/UX analyst with access to comprehensive webpage analysis data. You have analyzed a user's question using an advanced workflow that includes:

1. ✅ Query Understanding: Detected keywords: {', '.join(keywords)}
2. ✅ Vector Search: Found {len(coordinate_matches)} relevant UI elements
3. ✅ Index Extraction: Retrieved {len(dom_context)} complete DOM elements
4. ✅ Context Integration: Combined coordinate and DOM information
5. ✅ Position Awareness: Included exact pixel coordinates

User Question: {query}

COMPREHENSIVE UI ELEMENT ANALYSIS:
{combined_context}

ANALYSIS WORKFLOW SUMMARY:
- Keywords detected: {', '.join(keywords)}
- Elements found: {len(coordinate_matches)} coordinate matches
- DOM data retrieved: {len(dom_context)} complete elements
- Position data: Exact pixel coordinates included

Please provide a comprehensive, position-aware response that:
1. Directly addresses the user's question using the detected keywords
2. References specific UI elements by their exact positions and properties
3. Explains the functionality, purpose, and technical details of the elements
4. Provides precise coordinate information (x, y, width, height)
5. Includes relevant DOM information (HTML tags, CSS selectors, XPath)
6. Uses clear, user-friendly language while being technically accurate
7. Mentions how the element relates to the overall page structure

Response:
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"❌ Error generating response: {str(e)}"

    def initialize_data(self, coordinates_data, element_info_data):
        """Initialize the enhanced system with UI data"""
        print(f"\n📊 Initializing enhanced vector database system...")
        print(f"📍 Coordinates: {len(coordinates_data)} elements")
        print(f"🏗️ Element info: {len(element_info_data)} elements")

        self.processor.process_and_store_data(coordinates_data, element_info_data)
        print("✅ Enhanced UI data initialized successfully!")

print("✅ UIAnalyzer class defined!")

# Add workflow methods to UIElementProcessor
def analyze_query_and_retrieve_context(self, query: str, top_k: int = None) -> Dict[str, Any]:
    """
    Enhanced query analysis following the specified workflow:
    1. Query Understanding: Detect keywords
    2. Vector Search in Coordinates DB: Search for labels
    3. Index Extraction: Identify indices
    4. DOM Data Retrieval: Get corresponding DOM data
    5. Context Injection: Combine coordinate and DOM information
    """
    if top_k is None:
        top_k = self.config.TOP_K_RESULTS

    # Step 1: Query Understanding - Extract keywords
    keywords = self._extract_keywords(query)
    print(f"🔍 Detected keywords: {keywords}")

    # Step 2: Vector Search in Coordinates DB
    coordinate_results = self._search_coordinates(query, top_k)
    print(f"📍 Found {len(coordinate_results)} relevant coordinate matches")

    # Step 3: Index Extraction
    relevant_indices = self._extract_indices_from_coordinates(coordinate_results)
    print(f"📊 Extracted indices: {relevant_indices}")

    # Step 4: DOM Data Retrieval using indices
    dom_context = self._retrieve_dom_data_by_indices(relevant_indices)
    print(f"🏗️ Retrieved DOM data for {len(dom_context)} elements")

    # Step 5: Context Injection - Combine coordinate and DOM information
    combined_context = self._combine_coordinate_and_dom_context(coordinate_results, dom_context)

    return {
        "keywords": keywords,
        "coordinate_matches": coordinate_results,
        "relevant_indices": relevant_indices,
        "dom_context": dom_context,
        "combined_context": combined_context,
        "query": query
    }

def _extract_keywords(self, query: str) -> List[str]:
    """Extract relevant keywords from the user query"""
    # Convert to lowercase and split
    words = re.findall(r'\b\w+\b', query.lower())

    # Filter out common stop words
    stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}
    keywords = [word for word in words if word not in stop_words and len(word) > 2]

    return keywords

def _search_coordinates(self, query: str, top_k: int) -> List[Dict[str, Any]]:
    """Search for relevant coordinates using vector search"""
    if self.embeddings is not None:
        # Use vector search if embeddings are available
        results = self.coordinates_collection.query(
            query_texts=[query],
            n_results=top_k
        )

        coordinate_results = []
        if results['documents'] and results['documents'][0]:
            for i, doc in enumerate(results['documents'][0]):
                metadata = results['metadatas'][0][i]
                coordinate_results.append({
                    "document": doc,
                    "metadata": metadata,
                    "distance": results['distances'][0][i] if 'distances' in results else None
                })
    else:
        # Fallback to simple text matching
        all_items = self.coordinates_collection.get()
        coordinate_results = []

        query_lower = query.lower()
        for i, doc in enumerate(all_items['documents']):
            metadata = all_items['metadatas'][i]
            # Simple keyword matching
            if any(keyword in doc.lower() for keyword in query_lower.split()):
                coordinate_results.append({
                    "document": doc,
                    "metadata": metadata,
                    "distance": 0.5  # Default similarity score
                })

        # Limit results
        coordinate_results = coordinate_results[:top_k]

    return coordinate_results

# Bind workflow methods to the class
UIElementProcessor.analyze_query_and_retrieve_context = analyze_query_and_retrieve_context
UIElementProcessor._extract_keywords = _extract_keywords
UIElementProcessor._search_coordinates = _search_coordinates

print("✅ Workflow methods added to UIElementProcessor!")

# Add remaining helper methods
def _extract_indices_from_coordinates(self, coordinate_results: List[Dict[str, Any]]) -> List[int]:
    """Extract indices from coordinate search results"""
    indices = []
    for result in coordinate_results:
        metadata = result["metadata"]
        if "index" in metadata:
            indices.append(metadata["index"])
    return list(set(indices))  # Remove duplicates

def _retrieve_dom_data_by_indices(self, indices: List[int]) -> List[Dict[str, Any]]:
    """Retrieve complete DOM data using the extracted indices"""
    dom_context = []

    for index in indices:
        element_key = f"element_{index + 1}"
        if element_key in self.element_info_data:
            # Get the complete DOM information
            element_info = self.element_info_data[element_key]

            # Also get coordinate information for this index
            coordinate_info = None
            if index < len(self.coordinates_data):
                coordinate_info = self.coordinates_data[index]

            dom_context.append({
                "index": index,
                "element_key": element_key,
                "element_info": element_info,
                "coordinate_info": coordinate_info
            })

    return dom_context

def _combine_coordinate_and_dom_context(self, coordinate_results: List[Dict[str, Any]], dom_context: List[Dict[str, Any]]) -> str:
    """Combine coordinate and DOM information into a comprehensive context"""
    context_parts = []

    # Create a mapping of indices to DOM data for easy lookup
    dom_by_index = {item["index"]: item for item in dom_context}

    for coord_result in coordinate_results:
        coord_metadata = coord_result["metadata"]
        index = coord_metadata.get("index", -1)

        if index in dom_by_index:
            dom_item = dom_by_index[index]
            element_info = dom_item["element_info"]

            # Extract key computed style properties for better context
            computed_style = element_info.get('computedStyle', {})
            key_styles = {}
            important_style_props = ['display', 'position', 'width', 'height', 'color', 'background-color',
                                   'font-size', 'font-weight', 'border-radius', 'opacity', 'z-index']
            for prop in important_style_props:
                if prop in computed_style:
                    key_styles[prop] = computed_style[prop]

            context_part = f"""
Element {index + 1} ({coord_metadata['label']}):
POSITION & SIZE:
- Coordinates: ({coord_metadata['x']}, {coord_metadata['y']})
- Dimensions: {coord_metadata['width']}px × {coord_metadata['height']}px
- Area: {coord_metadata['width'] * coord_metadata['height']} square pixels

DOM STRUCTURE:
- HTML Tag: <{element_info.get('tag', 'unknown')}>
- Text Content: "{element_info.get('text', 'No text content')}"
- CSS Selector: {element_info.get('cssSelector', 'N/A')}
- XPath: {element_info.get('xpath', 'N/A')}

STYLING & ATTRIBUTES:
- CSS Classes: {', '.join(element_info.get('classes', [])) or 'None'}
- Key Attributes: {', '.join([f"{k}='{v}'" for k, v in element_info.get('attributes', {}).items() if v]) or 'None'}
- Important Styles: {', '.join([f"{k}: {v}" for k, v in key_styles.items()]) or 'Default styles'}

FUNCTIONALITY:
- Source URL: {element_info.get('src', 'N/A')}
- Link Target: {element_info.get('href', 'N/A')}
- Inline Styles: {element_info.get('inlineStyle', 'None')}
"""
            context_parts.append(context_part.strip())

    return "\n\n".join(context_parts)

# Bind remaining methods to the class
UIElementProcessor._extract_indices_from_coordinates = _extract_indices_from_coordinates
UIElementProcessor._retrieve_dom_data_by_indices = _retrieve_dom_data_by_indices
UIElementProcessor._combine_coordinate_and_dom_context = _combine_coordinate_and_dom_context

print("✅ All helper methods added to UIElementProcessor!")

# Initialize the UI Analyzer with sample data
print("🚀 Initializing Enhanced UI Element Analyzer")
print("=" * 60)

# Create analyzer instance
analyzer = UIAnalyzer()

# Initialize with sample data
analyzer.initialize_data(sample_coordinates, sample_element_info)

print("\n🎉 System ready for queries!")
print("\n📋 Available sample elements:")
for i, coord in enumerate(sample_coordinates):
    print(f"  {i+1}. {coord['label']} at ({coord['coordinates']['x']}, {coord['coordinates']['y']})")

# Interactive query interface
def create_query_interface():
    """Create an interactive interface for querying UI elements"""
    
    # Example queries showcasing intelligent label detection
    example_queries = [
        "What is the video element on the page?",
        "Where is the main heading shown on the page?",
        "Tell me about the figure element and its location",
        "What are the video attributes and properties?",
        "Show me the CSS classes for the heading element",
        "What is the XPath for the video element?",
        "Find the button element",
        "Show me the navigation bar",
        "video properties and details",
        "heading element information"
    ]
    
    # Create dropdown for example queries
    query_dropdown = widgets.Dropdown(
        options=["Select an example..."] + example_queries,
        value="Select an example...",
        description='Examples:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='100%')
    )
    
    # Create text area for custom queries
    query_text = widgets.Textarea(
        value='',
        placeholder='Enter your question about UI elements...',
        description='Your Query:',
        style={'description_width': 'initial'},
        layout=widgets.Layout(width='100%', height='100px')
    )
    
    # Create analyze button
    analyze_button = widgets.Button(
        description='🔍 Analyze Query',
        button_style='primary',
        layout=widgets.Layout(width='200px')
    )
    
    # Create output area
    output_area = widgets.Output()
    
    def on_dropdown_change(change):
        if change['new'] != "Select an example...":
            query_text.value = change['new']
    
    def on_analyze_click(button):
        with output_area:
            clear_output(wait=True)
            
            query = query_text.value.strip()
            if not query:
                print("⚠️ Please enter a query first!")
                return
            
            try:
                print(f"🔍 Processing query: '{query}'")
                print("=" * 60)
                
                # Analyze the query
                response = analyzer.analyze_query(query)
                
                print("\n🤖 AI Response:")
                print("=" * 60)
                print(response)
                
            except Exception as e:
                print(f"❌ Error processing query: {str(e)}")
    
    # Bind events
    query_dropdown.observe(on_dropdown_change, names='value')
    analyze_button.on_click(on_analyze_click)
    
    # Display interface
    display(widgets.VBox([
        widgets.HTML("<h3>🔍 Enhanced UI Element Query Interface</h3>"),
        widgets.HTML("<p>Ask questions about UI elements and get detailed, position-aware responses!</p>"),
        query_dropdown,
        query_text,
        analyze_button,
        output_area
    ]))

# Create and display the interface
create_query_interface()

from google.colab import files
import json

def upload_custom_data():
    """Upload and process custom UI data files"""
    
    print("📁 Upload Your Custom UI Data Files")
    print("=" * 50)
    print("Please upload the following files:")
    print("1. coordinates.json - UI element positions and labels")
    print("2. element_info.json - Complete DOM data for elements")
    print("\nNote: Files should follow the same format as the sample data.")
    
    # Upload coordinates file
    print("\n📍 Upload coordinates.json:")
    coord_files = files.upload()
    
    # Upload element info file
    print("\n🏗️ Upload element_info.json:")
    element_files = files.upload()
    
    try:
        # Process uploaded files
        coord_filename = list(coord_files.keys())[0]
        element_filename = list(element_files.keys())[0]
        
        # Load the data
        with open(coord_filename, 'r') as f:
            custom_coordinates = json.load(f)
        
        with open(element_filename, 'r') as f:
            custom_element_info = json.load(f)
        
        print(f"\n✅ Files loaded successfully!")
        print(f"📊 Coordinates: {len(custom_coordinates)} elements")
        print(f"🏗️ Element info: {len(custom_element_info)} elements")
        
        # Initialize analyzer with custom data
        print("\n🔄 Initializing analyzer with your data...")
        analyzer.initialize_data(custom_coordinates, custom_element_info)
        
        print("\n🎉 Your custom data is now ready for analysis!")
        print("Use the query interface above to ask questions about your UI elements.")
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing uploaded files: {str(e)}")
        print("Please check that your files are in the correct JSON format.")
        return False

# Create upload button
upload_button = widgets.Button(
    description='📁 Upload Custom Data',
    button_style='info',
    layout=widgets.Layout(width='200px')
)

upload_output = widgets.Output()

def on_upload_click(button):
    with upload_output:
        clear_output(wait=True)
        upload_custom_data()

upload_button.on_click(on_upload_click)

display(widgets.VBox([
    widgets.HTML("<h3>📁 Custom Data Upload</h3>"),
    widgets.HTML("<p>Upload your own coordinates.json and element_info.json files to analyze custom web pages.</p>"),
    upload_button,
    upload_output
]))