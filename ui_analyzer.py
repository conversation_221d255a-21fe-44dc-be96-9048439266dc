import json
from typing import Dict, List, Any, Optional, <PERSON><PERSON>
import chromadb
from sentence_transformers import SentenceTransformer
import google.generativeai as genai
from config import Config
import re

class UIElementProcessor:
    """Enhanced processor for UI elements with comprehensive vector database storage"""

    def __init__(self):
        self.config = Config()
        # Set Hugging Face token for model downloads
        import os
        os.environ["HUGGINGFACE_HUB_TOKEN"] = self.config.HUGGINGFACE_TOKEN

        try:
            self.embeddings = SentenceTransformer(
                self.config.EMBEDDING_MODEL,
                token=self.config.HUGGINGFACE_TOKEN
            )
            print("✅ SentenceTransformer loaded successfully!")
        except Exception as e:
            # Fallback to a simpler approach without embeddings
            print(f"Warning: Could not load SentenceTransformer: {e}. Using simple text matching.")
            self.embeddings = None

        self.chroma_client = chromadb.PersistentClient(path=self.config.CHROMA_DB_PATH)
        self.coordinates_collection = None
        self.dom_collection = None
        self._initialize_collections()

        # Store raw data for index-based retrieval
        self.coordinates_data = []
        self.element_info_data = {}

    def _initialize_collections(self):
        """Initialize or get existing ChromaDB collections for coordinates and DOM data"""
        # Initialize coordinates collection
        try:
            self.coordinates_collection = self.chroma_client.get_collection(name="ui_coordinates")
            print("✅ Loaded existing coordinates collection")
        except:
            self.coordinates_collection = self.chroma_client.create_collection(
                name="ui_coordinates",
                metadata={"description": "UI element coordinates and labels for semantic search"}
            )
            print("✅ Created new coordinates collection")

        # Initialize DOM collection
        try:
            self.dom_collection = self.chroma_client.get_collection(name="ui_dom_data")
            print("✅ Loaded existing DOM collection")
        except:
            self.dom_collection = self.chroma_client.create_collection(
                name="ui_dom_data",
                metadata={"description": "Complete DOM element information indexed by position"}
            )
            print("✅ Created new DOM collection")

    def process_and_store_data(self, coordinates_path: str, element_info_path: str):
        """Enhanced processing and storage of UI element data in separate vector databases"""
        # Load coordinates data
        with open(coordinates_path, 'r') as f:
            self.coordinates_data = json.load(f)

        # Load element info data
        with open(element_info_path, 'r') as f:
            self.element_info_data = json.load(f)

        # Clear existing data
        try:
            self.coordinates_collection.delete()
            self.dom_collection.delete()
        except:
            pass

        # Store coordinates data for semantic search
        self._store_coordinates_data()

        # Store complete DOM data indexed by position
        self._store_dom_data()

        print(f"✅ Stored {len(self.coordinates_data)} coordinate elements and {len(self.element_info_data)} DOM elements")

    def _store_coordinates_data(self):
        """Store coordinates data in vector database for semantic search"""
        coord_documents = []
        coord_metadatas = []
        coord_ids = []

        for i, coord_item in enumerate(self.coordinates_data):
            # Create searchable text for coordinates
            coord_text = self._create_coordinate_search_text(coord_item, i)

            # Create metadata for coordinates
            coord_metadata = {
                "index": i,
                "element_id": f"element_{i+1}",
                "label": coord_item["label"],
                "x": coord_item["coordinates"]["x"],
                "y": coord_item["coordinates"]["y"],
                "width": coord_item["coordinates"]["width"],
                "height": coord_item["coordinates"]["height"]
            }

            coord_documents.append(coord_text)
            coord_metadatas.append(coord_metadata)
            coord_ids.append(f"coord_{i}")

        # Store coordinates in ChromaDB
        if coord_documents:
            self.coordinates_collection.add(
                documents=coord_documents,
                metadatas=coord_metadatas,
                ids=coord_ids
            )
            print(f"✅ Stored {len(coord_documents)} coordinate elements for semantic search")

    def _store_dom_data(self):
        """Store complete DOM data indexed by position"""
        dom_documents = []
        dom_metadatas = []
        dom_ids = []

        for element_key, element_info in self.element_info_data.items():
            # Extract index from element_key (e.g., "element_1" -> 0)
            index = int(element_key.split('_')[1]) - 1

            # Create comprehensive DOM text for embedding
            dom_text = self._create_dom_search_text(element_info, index)

            # Create comprehensive metadata including all DOM information
            dom_metadata = {
                "index": index,
                "element_id": element_key,
                "tag": element_info.get("tag", ""),
                "text": element_info.get("text", ""),
                "css_selector": element_info.get("cssSelector", ""),
                "xpath": element_info.get("xpath", ""),
                "classes": json.dumps(element_info.get("classes", [])),
                "attributes": json.dumps(element_info.get("attributes", {})),
                "computed_style_keys": json.dumps(list(element_info.get("computedStyle", {}).keys())[:50])  # Limit for metadata
            }

            dom_documents.append(dom_text)
            dom_metadatas.append(dom_metadata)
            dom_ids.append(f"dom_{index}")

        # Store DOM data in ChromaDB
        if dom_documents:
            self.dom_collection.add(
                documents=dom_documents,
                metadatas=dom_metadatas,
                ids=dom_ids
            )
            print(f"✅ Stored {len(dom_documents)} DOM elements with complete information")

    def _create_coordinate_search_text(self, coord_item: Dict, index: int) -> str:
        """Create searchable text for coordinate data"""
        text_parts = [
            f"Label: {coord_item['label']}",
            f"Element type: {coord_item['label'].lower()}",
            f"UI element: {coord_item['label']}",
            f"Position: x={coord_item['coordinates']['x']}, y={coord_item['coordinates']['y']}",
            f"Size: {coord_item['coordinates']['width']}x{coord_item['coordinates']['height']}",
            f"Index: {index}",
            f"Element number: {index + 1}"
        ]
        return " | ".join(text_parts)

    def _create_dom_search_text(self, element_info: Dict, index: int) -> str:
        """Create comprehensive searchable text for DOM data"""
        text_parts = [
            f"Tag: {element_info.get('tag', '')}",
            f"Text content: {element_info.get('text', '')}",
            f"CSS classes: {' '.join(element_info.get('classes', []))}",
            f"CSS selector: {element_info.get('cssSelector', '')}",
            f"XPath: {element_info.get('xpath', '')}",
            f"Index: {index}",
            f"Element number: {index + 1}"
        ]

        # Add attributes if available
        if 'attributes' in element_info:
            attrs = element_info['attributes']
            if attrs:
                attr_text = ", ".join([f"{k}={v}" for k, v in attrs.items() if v])
                text_parts.append(f"Attributes: {attr_text}")

        # Add some computed style information for better searchability
        if 'computedStyle' in element_info:
            computed_style = element_info['computedStyle']
            # Include key style properties that might be relevant for search
            relevant_styles = ['display', 'position', 'width', 'height', 'color', 'background-color', 'font-size']
            style_parts = []
            for style in relevant_styles:
                if style in computed_style:
                    style_parts.append(f"{style}: {computed_style[style]}")
            if style_parts:
                text_parts.append(f"Key styles: {', '.join(style_parts)}")

        return " | ".join(text_parts)

    def analyze_query_and_retrieve_context(self, query: str, top_k: int = None) -> Dict[str, Any]:
        """
        Enhanced query analysis following the specified workflow:
        1. Query Understanding: Detect keywords
        2. Vector Search in Coordinates DB: Search for labels
        3. Index Extraction: Identify indices
        4. DOM Data Retrieval: Get corresponding DOM data
        5. Context Injection: Combine coordinate and DOM information
        """
        if top_k is None:
            top_k = self.config.TOP_K_RESULTS

        # Step 1: Query Understanding - Extract keywords
        keywords = self._extract_keywords(query)
        print(f"🔍 Detected keywords: {keywords}")

        # Step 2: Vector Search in Coordinates DB
        coordinate_results = self._search_coordinates(query, top_k)
        print(f"📍 Found {len(coordinate_results)} relevant coordinate matches")

        # Step 3: Index Extraction
        relevant_indices = self._extract_indices_from_coordinates(coordinate_results)
        print(f"📊 Extracted indices: {relevant_indices}")

        # Step 4: DOM Data Retrieval using indices
        dom_context = self._retrieve_dom_data_by_indices(relevant_indices)
        print(f"🏗️ Retrieved DOM data for {len(dom_context)} elements")

        # Step 5: Context Injection - Combine coordinate and DOM information
        combined_context = self._combine_coordinate_and_dom_context(coordinate_results, dom_context)

        return {
            "keywords": keywords,
            "coordinate_matches": coordinate_results,
            "relevant_indices": relevant_indices,
            "dom_context": dom_context,
            "combined_context": combined_context,
            "query": query
        }

    def _extract_keywords(self, query: str) -> List[str]:
        """Extract relevant keywords from the user query"""
        # Convert to lowercase and split
        words = re.findall(r'\b\w+\b', query.lower())

        # Filter out common stop words
        stop_words = {'the', 'is', 'at', 'which', 'on', 'a', 'an', 'and', 'or', 'but', 'in', 'with', 'to', 'for', 'of', 'as', 'by', 'what', 'where', 'how', 'when', 'why', 'who'}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]

        return keywords

    def _search_coordinates(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """Search for relevant coordinates using vector search"""
        if self.embeddings is not None:
            # Use vector search if embeddings are available
            results = self.coordinates_collection.query(
                query_texts=[query],
                n_results=top_k
            )

            coordinate_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i]
                    coordinate_results.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": results['distances'][0][i] if 'distances' in results else None
                    })
        else:
            # Fallback to simple text matching
            all_items = self.coordinates_collection.get()
            coordinate_results = []

            query_lower = query.lower()
            for i, doc in enumerate(all_items['documents']):
                metadata = all_items['metadatas'][i]
                # Simple keyword matching
                if any(keyword in doc.lower() for keyword in query_lower.split()):
                    coordinate_results.append({
                        "document": doc,
                        "metadata": metadata,
                        "distance": 0.5  # Default similarity score
                    })

            # Limit results
            coordinate_results = coordinate_results[:top_k]

        return coordinate_results

    def _extract_indices_from_coordinates(self, coordinate_results: List[Dict[str, Any]]) -> List[int]:
        """Extract indices from coordinate search results"""
        indices = []
        for result in coordinate_results:
            metadata = result["metadata"]
            if "index" in metadata:
                indices.append(metadata["index"])
        return list(set(indices))  # Remove duplicates

    def _retrieve_dom_data_by_indices(self, indices: List[int]) -> List[Dict[str, Any]]:
        """Retrieve complete DOM data using the extracted indices"""
        dom_context = []

        for index in indices:
            element_key = f"element_{index + 1}"
            if element_key in self.element_info_data:
                # Get the complete DOM information
                element_info = self.element_info_data[element_key]

                # Also get coordinate information for this index
                coordinate_info = None
                if index < len(self.coordinates_data):
                    coordinate_info = self.coordinates_data[index]

                dom_context.append({
                    "index": index,
                    "element_key": element_key,
                    "element_info": element_info,
                    "coordinate_info": coordinate_info
                })

        return dom_context

    def _combine_coordinate_and_dom_context(self, coordinate_results: List[Dict[str, Any]], dom_context: List[Dict[str, Any]]) -> str:
        """Combine coordinate and DOM information into a comprehensive context"""
        context_parts = []

        # Create a mapping of indices to DOM data for easy lookup
        dom_by_index = {item["index"]: item for item in dom_context}

        for coord_result in coordinate_results:
            coord_metadata = coord_result["metadata"]
            index = coord_metadata.get("index", -1)

            if index in dom_by_index:
                dom_item = dom_by_index[index]
                element_info = dom_item["element_info"]

                context_part = f"""
Element {index + 1} ({coord_metadata['label']}):
- Position: ({coord_metadata['x']}, {coord_metadata['y']})
- Size: {coord_metadata['width']}x{coord_metadata['height']}
- HTML Tag: {element_info.get('tag', 'N/A')}
- Text Content: "{element_info.get('text', 'N/A')}"
- CSS Selector: {element_info.get('cssSelector', 'N/A')}
- XPath: {element_info.get('xpath', 'N/A')}
- CSS Classes: {', '.join(element_info.get('classes', []))}
- Key Attributes: {', '.join([f"{k}={v}" for k, v in element_info.get('attributes', {}).items() if v])}
"""
                context_parts.append(context_part.strip())

        return "\n\n".join(context_parts)

class UIAnalyzer:
    """Enhanced UI analyzer using Gemini AI with comprehensive vector database workflow"""

    def __init__(self):
        self.config = Config()
        self.processor = UIElementProcessor()

        # Configure Gemini
        genai.configure(api_key=self.config.GOOGLE_API_KEY)
        self.model = genai.GenerativeModel(self.config.MODEL_NAME)
        print("✅ UIAnalyzer initialized with enhanced vector database workflow!")

    def analyze_query(self, query: str, screenshot_path: str = None) -> str:
        """
        Enhanced query analysis following the complete workflow:
        1. Query Understanding: Detect keywords
        2. Vector Search in Coordinates DB: Search for labels
        3. Index Extraction: Identify indices
        4. DOM Data Retrieval: Get corresponding DOM data
        5. Context Injection: Store DOM data and pass as context
        6. Coordinate Inclusion: Include position information
        7. Model Response: Generate position-aware response
        """
        if screenshot_path is None:
            screenshot_path = self.config.DEFAULT_SCREENSHOT_PATH

        print(f"\n🔍 Analyzing query: '{query}'")
        print("=" * 50)

        # Use the enhanced workflow
        analysis_result = self.processor.analyze_query_and_retrieve_context(query)

        # Generate response using Gemini with enhanced context
        response = self._generate_enhanced_response(query, analysis_result, screenshot_path)

        return response

    def _generate_enhanced_response(self, query: str, analysis_result: Dict[str, Any], screenshot_path: str) -> str:
        """Generate response using Gemini with enhanced context from the workflow"""

        # Extract information from analysis result
        keywords = analysis_result["keywords"]
        combined_context = analysis_result["combined_context"]
        coordinate_matches = analysis_result["coordinate_matches"]
        dom_context = analysis_result["dom_context"]

        # Create enhanced prompt with workflow information
        prompt = f"""
You are an expert UI/UX analyst with access to comprehensive webpage analysis data. You have analyzed a user's question using an advanced workflow that includes:

1. ✅ Query Understanding: Detected keywords: {', '.join(keywords)}
2. ✅ Vector Search: Found {len(coordinate_matches)} relevant UI elements
3. ✅ Index Extraction: Retrieved {len(dom_context)} complete DOM elements
4. ✅ Context Integration: Combined coordinate and DOM information
5. ✅ Position Awareness: Included exact pixel coordinates

User Question: {query}

Screenshot Reference: The user has provided a screenshot at {screenshot_path}

COMPREHENSIVE UI ELEMENT ANALYSIS:
{combined_context}

ANALYSIS WORKFLOW SUMMARY:
- Keywords detected: {', '.join(keywords)}
- Elements found: {len(coordinate_matches)} coordinate matches
- DOM data retrieved: {len(dom_context)} complete elements
- Position data: Exact pixel coordinates included

Please provide a comprehensive, position-aware response that:
1. Directly addresses the user's question using the detected keywords
2. References specific UI elements by their exact positions and properties
3. Explains the functionality, purpose, and technical details of the elements
4. Provides precise coordinate information (x, y, width, height)
5. Includes relevant DOM information (HTML tags, CSS selectors, XPath)
6. Uses clear, user-friendly language while being technically accurate
7. Mentions how the element relates to the overall page structure

Response:
"""

        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"❌ Error generating response: {str(e)}"

    def initialize_data(self, coordinates_path: str = None, element_info_path: str = None):
        """Initialize the enhanced system with UI data"""
        if coordinates_path is None:
            coordinates_path = self.config.DEFAULT_COORDINATES_PATH
        if element_info_path is None:
            element_info_path = self.config.DEFAULT_ELEMENT_INFO_PATH

        print(f"\n📊 Initializing enhanced vector database system...")
        print(f"📍 Coordinates file: {coordinates_path}")
        print(f"🏗️ Element info file: {element_info_path}")

        self.processor.process_and_store_data(coordinates_path, element_info_path)
        print("✅ Enhanced UI data initialized successfully!")

    def get_analysis_summary(self, query: str) -> Dict[str, Any]:
        """Get detailed analysis summary without generating AI response"""
        return self.processor.analyze_query_and_retrieve_context(query)

def main():
    """Enhanced main function for testing the new workflow"""
    print("🚀 Starting Enhanced UI Element Analyzer")
    print("=" * 60)

    # Initialize the analyzer
    analyzer = UIAnalyzer()

    # Initialize with default data
    analyzer.initialize_data()

    # Enhanced example queries that test the new workflow
    test_queries = [
        "What is the video element on the page?",
        "Where is the main heading shown on the page?",
        "Tell me about the figure element and its location",
        "What are the video attributes and properties?",
        "Show me the CSS classes for the heading element",
        "What is the XPath for the video element?",
        "Where can I find elements with autoplay functionality?"
    ]

    print(f"\n🧪 Testing Enhanced Workflow with {len(test_queries)} queries")
    print("=" * 60)

    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}/{len(test_queries)}: {query}")
        print("-" * 50)

        try:
            # Get analysis summary first
            analysis = analyzer.get_analysis_summary(query)
            print(f"📊 Keywords: {', '.join(analysis['keywords'])}")
            print(f"📍 Coordinate matches: {len(analysis['coordinate_matches'])}")
            print(f"🏗️ DOM elements retrieved: {len(analysis['dom_context'])}")

            # Generate AI response
            response = analyzer.analyze_query(query)
            print(f"\n🤖 AI Response:\n{response}")

        except Exception as e:
            print(f"❌ Error processing query: {str(e)}")

        print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
